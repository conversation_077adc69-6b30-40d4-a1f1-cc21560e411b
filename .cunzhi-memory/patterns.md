# 常用模式和最佳实践

- SituationComponentEngine性能优化：将fillData方法中的bizInstanceSituationApi.save()调用改为异步执行，使用@Async注解和CompletableFuture，避免阻塞主线程，提升响应性能。原耗时3089ms的同步操作现在异步执行。
- 解决Spring AOP代理问题：使用CompletableFuture.runAsync()替代@Async注解实现异步执行，避免JDK动态代理导致的ClassCastException。这种方式不会创建代理对象，保持原始类型。
- 解决异步线程请求上下文问题：使用Spring事件发布机制替代直接异步调用。创建SituationDataSaveEvent事件和SituationDataSaveListener监听器，在主线程发布事件，在异步监听器中处理保存逻辑，避免RequestContextHolder为null的问题。
- BizInstanceSituationApi:save() 方法已使用 CompletableFuture 替代 CountDownLatch 解决线程阻塞问题，提供更好的异常处理和任务管理
