package com.workplat.componentEngine.listener;

import com.workplat.componentEngine.event.SituationDataSaveEvent;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.dubbo.vo.BizInstanceQuotaVO;
import com.workplat.gss.common.core.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 情境数据保存事件监听器
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Slf4j
@Component
public class SituationDataSaveListener {

    @Autowired
    private BizInstanceSituationApi bizInstanceSituationApi;

    /**
     * 异步处理情境数据保存事件
     *
     * @param event 情境数据保存事件
     */
    @Async("asyncExecutor")
    @EventListener
    public void handleSituationDataSave(SituationDataSaveEvent event) {
        try {
            log.info("=== 接收到情境数据保存事件 ===");
            log.info("线程名称: {}", Thread.currentThread().getName());
            log.info("开始异步保存情境数据，实例ID: {}, 数据量: {}",
                event.getInstanceId(), event.getBizInstanceQuotas().size());
            long startTime = System.currentTimeMillis();

            // 调用保存方法
            ResponseData<List<BizInstanceQuotaVO>> result = 
                bizInstanceSituationApi.save(event.getBizInstanceQuotas());

            long endTime = System.currentTimeMillis();
            log.info("异步保存情境数据完成，实例ID: {}, 耗时: {}ms, 结果: {}",
                event.getInstanceId(), endTime - startTime, 
                result.getCode() == 200 ? "成功" : "失败");

            if (result.getCode() != 200) {
                log.error("异步保存情境数据失败，实例ID: {}, 错误信息: {}", 
                    event.getInstanceId(), result.getMessage());
            }

        } catch (Exception e) {
            log.error("异步保存情境数据异常，实例ID: {}, 异常信息: {}", 
                event.getInstanceId(), e.getMessage(), e);
            // 这里可以添加重试机制或者发送告警
        }
    }
}
