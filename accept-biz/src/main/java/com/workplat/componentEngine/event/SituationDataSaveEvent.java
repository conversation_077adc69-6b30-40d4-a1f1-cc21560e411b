package com.workplat.componentEngine.event;

import com.workplat.gss.application.api.dto.BizInstanceQuotaDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 情境数据保存事件
 *
 * <AUTHOR>
 * @date 2025/06/19
 */
@Getter
public class SituationDataSaveEvent extends ApplicationEvent {

    /**
     * 需要保存的情境数据
     */
    private final List<BizInstanceQuotaDto> bizInstanceQuotas;

    /**
     * 实例ID
     */
    private final String instanceId;

    public SituationDataSaveEvent(Object source, List<BizInstanceQuotaDto> bizInstanceQuotas, String instanceId) {
        super(source);
        this.bizInstanceQuotas = bizInstanceQuotas;
        this.instanceId = instanceId;
    }
}
