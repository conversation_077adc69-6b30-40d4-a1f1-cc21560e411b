package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.workplat.utils.FormStepProcessor;

import java.util.HashMap;
import java.util.Map;

/**
 * FormGroupComponentEngine 智能表单处理测试
 */
public class FormGroupComponentEngineTest {

    public static void main(String[] args) {
        System.out.println("=== FormGroupComponentEngine 智能表单处理测试 ===");

        // 模拟表单JSON
        String formJson = """
            {
                "tableName": "test_form",
                "formAttribute": {},
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "第一步：基本信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "name", "label": "姓名", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "age", "label": "年龄", "modelValue": ""}
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "第二步：联系信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "phone", "label": "电话", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "email", "label": "邮箱", "modelValue": ""}
                            }
                        ]
                    }
                ]
            }
            """;

        // 测试场景1：用户刚开始填写表单
        System.out.println("\n--- 测试场景1：用户刚开始填写表单 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        testFormProcessing("刚开始填写", formJson, emptyFields);

        // 测试场景2：用户填写了姓名
        System.out.println("\n--- 测试场景2：用户填写了姓名 ---");
        Map<String, Object> nameField = new HashMap<>();
        nameField.put("name", "张三");
        testFormProcessing("填写了姓名", formJson, nameField);

        // 测试场景3：用户完成了第一步
        System.out.println("\n--- 测试场景3：用户完成了第一步 ---");
        Map<String, Object> step1Complete = new HashMap<>();
        step1Complete.put("name", "张三");
        step1Complete.put("age", "25");
        testFormProcessing("完成第一步", formJson, step1Complete);

        // 测试场景4：用户完成了所有字段
        System.out.println("\n--- 测试场景4：用户完成了所有字段 ---");
        Map<String, Object> allComplete = new HashMap<>();
        allComplete.put("name", "张三");
        allComplete.put("age", "25");
        allComplete.put("phone", "13800138000");
        allComplete.put("email", "<EMAIL>");
        testFormProcessing("完成所有字段", formJson, allComplete);

        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 测试表单处理逻辑
     */
    private static void testFormProcessing(String scenario, String formJson, Map<String, Object> filledFields) {
        System.out.println("场景：" + scenario);
        System.out.println("已填写字段：" + filledFields);

        // 模拟 FormGroupComponentEngine.doExecute() 中的逻辑
        
        // 🎯 使用智能表单处理器获取需要填写的表单块
        String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);
        
        if (nextFormBlock.isEmpty()) {
            // 所有字段都已填写完成
            System.out.println("✅ 表单所有字段已填写完成");
            System.out.println("返回表单：{}");
            System.out.println("formCompleted: true");
        } else {
            // 设置需要填写的表单数据
            System.out.println("📝 需要填写的表单块：");
            System.out.println(formatJson(nextFormBlock));
            
            // 获取表单处理结果以更新步骤信息
            FormStepProcessor.FormProcessResult processResult = 
                FormStepProcessor.processFormWithFilledFields(formJson, filledFields);
            
            // 更新步骤信息
            int formStepCount = processResult.getTotalSteps();
            int formStepIndex = processResult.getNextIncompleteStep() == -1 ? 
                processResult.getTotalSteps() : processResult.getNextIncompleteStep();
            boolean formCompleted = processResult.isAllCompleted();
            
            System.out.println("formStepCount: " + formStepCount);
            System.out.println("formStepIndex: " + formStepIndex);
            System.out.println("formCompleted: " + formCompleted);
            
            System.out.println("智能表单处理 - 当前步骤: " + formStepIndex + "/" + formStepCount + 
                ", 是否完成: " + formCompleted);
        }
        
        System.out.println("---");
    }

    /**
     * 格式化JSON显示
     */
    private static String formatJson(String jsonStr) {
        try {
            return JSON.parseObject(jsonStr).toJSONString();
        } catch (Exception e) {
            return jsonStr;
        }
    }

    /**
     * 模拟 fillData 方法的逻辑
     */
    public static void testFillData(String formJson, Map<String, Object> existingFields, Map<String, Object> submitData) {
        System.out.println("\n=== 测试 fillData 方法逻辑 ===");
        System.out.println("原有字段：" + existingFields);
        System.out.println("提交数据：" + submitData);

        // 合并已填写的数据
        Map<String, Object> allFilledFields = new HashMap<>(existingFields);
        allFilledFields.putAll(submitData);
        
        System.out.println("合并后字段：" + allFilledFields);

        // 🎯 使用智能表单处理器检查表单完成状态
        boolean isFormCompleted = FormStepProcessor.isFormCompleted(formJson, allFilledFields);
        
        if (isFormCompleted) {
            System.out.println("🎉 表单填写完成！所有字段都已填写");
            System.out.println("formCompleted: true");
        } else {
            // 获取下一个需要填写的步骤
            int nextStep = FormStepProcessor.getNextIncompleteStep(formJson, allFilledFields);
            System.out.println("表单继续填写 - 下一步骤: " + nextStep);
            System.out.println("formCompleted: false");
        }
    }

    /**
     * 模拟 getNextInstruction 方法的逻辑
     */
    public static String testGetNextInstruction(Boolean formCompleted, Integer formStepCount, Integer formStepIndex) {
        System.out.println("\n=== 测试 getNextInstruction 方法逻辑 ===");
        System.out.println("formCompleted: " + formCompleted);
        System.out.println("formStepCount: " + formStepCount);
        System.out.println("formStepIndex: " + formStepIndex);

        if (formCompleted != null && formCompleted) {
            // 表单已完成，继续下一个指令
            System.out.println("表单已完成，继续执行下一个指令");
            return "NEXT_INSTRUCTION";
        }

        // 检查是否还有步骤需要填写
        if (formStepCount != null && 
            formStepIndex != null &&
            formStepIndex <= formStepCount) {
            // 还有步骤需要填写，保持当前组件
            System.out.println("表单还有步骤需要填写 - 当前步骤: " + formStepIndex + "/" + formStepCount);
            return "KEEP_AT_PRESENT";
        }

        // 默认继续下一个指令
        return "NEXT_INSTRUCTION";
    }
}
