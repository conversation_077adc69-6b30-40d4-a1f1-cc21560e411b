package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.accept.business.chat.vo.StreamMessageVO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 表单分组组件
 *
 * <AUTHOR>
 * @date 2025/06/04
 */
@Slf4j
@Service
public class FormGroupComponentEngine extends AbstractComponentEngine {

    private final BizInstanceFieldsService instanceFieldsService;
    private final ChatCacheUtil chatCacheUtil;


    public FormGroupComponentEngine(BizInstanceFieldsService instanceFieldsService, ChatCacheUtil chatCacheUtil) {
        this.instanceFieldsService = instanceFieldsService;
        this.chatCacheUtil = chatCacheUtil;
    }

    public static final String CODE = "FormFill";

    @Override
    protected ComponentRunVO doExecute() {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO =
                instanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);

        // 获取已填写的字段数据
        Map<String, Object> filledFields = getFilledFields(bizInstanceFieldsVO.getFormObj());

        // 🎯 使用智能表单处理器获取需要填写的表单块
        String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(bizInstanceFieldsVO.getFormJson(), filledFields);

        if (nextFormBlock.isEmpty()) {
            // 所有字段都已填写完成
            log.info("表单所有字段已填写完成");
            bizInstanceFieldsVO.setFormJson("{}"); // 返回空表单
            chatProcessDTO.setFormCompleted(true);
        } else {
            // 设置需要填写的表单数据
            bizInstanceFieldsVO.setFormJson(nextFormBlock);
            chatProcessDTO.setFormCompleted(false);
            log.info("智能表单处理 - 还有字段需要填写");
        }

        // 更新缓存
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        //  输出结果
        ComponentRunVO.RenderData fieldFormRenderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceFieldsVO)
                .build();
        // 组装结果
        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(fieldFormRenderData);
        vo.setRenderData(renderDataList);
        vo.setTips(nextFormBlock.isEmpty() ? "表单已完成" : "智能表单展示组件");
        return vo;
    }

    /**
     * 获取已填写的字段数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getFilledFields(String formObj) {
        if (formObj == null || formObj.trim().isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            return JSON.parseObject(formObj, Map.class);
        } catch (Exception e) {
            log.warn("解析已填写字段数据失败: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 填充数据
        log.info("智能表单组件填充数据");
        Object submitData = componentDataContext.getSubmitData();
        log.info("submitData:{}", JSON.toJSONString(submitData));

        // 更新填报的数据
        @SuppressWarnings("unchecked")
        Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
        if (submitDataMap != null) {
            BizInstanceFieldsVO bizInstanceFieldsVO =
                    instanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);

            // 合并已填写的数据
            String formObj = bizInstanceFieldsVO.getFormObj();
            Map<String, Object> allFilledFields;
            if (formObj != null && !formObj.trim().isEmpty()) {
                Map<String, Object> formObjMap = getFilledFields(formObj);
                formObjMap.putAll(submitDataMap);
                allFilledFields = formObjMap;
            } else {
                allFilledFields = submitDataMap;
            }

            // 更新数据库
            bizInstanceFieldsVO.setFormObj(JSON.toJSONString(allFilledFields));
            BizInstanceFields bizInstanceFields = instanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            bizInstanceFields.setFormObj(bizInstanceFieldsVO.getFormObj());
            instanceFieldsService.update(bizInstanceFields);

            // 🎯 使用智能表单处理器检查表单完成状态
            boolean isFormCompleted = FormStepProcessor.isFormCompleted(bizInstanceFieldsVO.getFormJson(), allFilledFields);

            // 更新缓存中的表单状态
            ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
            chatProcessDTO.setFormCompleted(isFormCompleted);

            if (isFormCompleted) {
                log.info("🎉 表单填写完成！所有字段都已填写");
            } else {
                log.info("表单继续填写 - 还有字段需要填写");
            }

            chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
            log.info("智能表单组件填充数据结束 - 是否完成: {}", isFormCompleted);
        }
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        if (chatProcessDTO != null) {
            // 🎯 基于智能表单处理结果判断是否继续
            Boolean formCompleted = chatProcessDTO.getFormCompleted();

            if (formCompleted != null && formCompleted) {
                // 表单已完成，继续下一个指令
                log.info("表单已完成，继续执行下一个指令");
                return super.getNextInstruction(componentDataContext);
            } else {
                // 表单未完成，保持当前组件
                log.info("表单还有字段需要填写，保持当前组件");
                return InstructionConstant.KEEP_AT_PRESENT.getCode();
            }
        }

        // 默认继续下一个指令
        return super.getNextInstruction(componentDataContext);
    }
}
