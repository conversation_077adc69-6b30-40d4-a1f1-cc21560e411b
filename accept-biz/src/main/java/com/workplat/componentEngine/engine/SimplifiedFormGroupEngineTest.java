package com.workplat.componentEngine.engine;

import com.workplat.utils.FormStepProcessor;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化版 FormGroupComponentEngine 测试
 * 只使用两个核心方法：getNextFormBlockToFill 和 isFormCompleted
 */
public class SimplifiedFormGroupEngineTest {

    public static void main(String[] args) {
        System.out.println("=== 简化版 FormGroupComponentEngine 测试 ===");

        // 模拟表单JSON
        String formJson = """
            {
                "tableName": "simplified_form",
                "formAttribute": {},
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "基本信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "name", "label": "姓名", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "age", "label": "年龄", "modelValue": ""}
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "联系信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "phone", "label": "电话", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "email", "label": "邮箱", "modelValue": ""}
                            }
                        ]
                    }
                ]
            }
            """;

        // 测试场景1：用户刚开始填写表单
        System.out.println("\n--- 场景1：用户刚开始填写表单 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        testSimplifiedLogic("刚开始填写", formJson, emptyFields);

        // 测试场景2：用户填写了姓名
        System.out.println("\n--- 场景2：用户填写了姓名 ---");
        Map<String, Object> nameField = new HashMap<>();
        nameField.put("name", "张三");
        testSimplifiedLogic("填写了姓名", formJson, nameField);

        // 测试场景3：用户完成了第一步
        System.out.println("\n--- 场景3：用户完成了第一步 ---");
        Map<String, Object> step1Complete = new HashMap<>();
        step1Complete.put("name", "张三");
        step1Complete.put("age", "25");
        testSimplifiedLogic("完成第一步", formJson, step1Complete);

        // 测试场景4：用户完成了所有字段
        System.out.println("\n--- 场景4：用户完成了所有字段 ---");
        Map<String, Object> allComplete = new HashMap<>();
        allComplete.put("name", "张三");
        allComplete.put("age", "25");
        allComplete.put("phone", "13800138000");
        allComplete.put("email", "<EMAIL>");
        testSimplifiedLogic("完成所有字段", formJson, allComplete);

        // 测试fillData逻辑
        System.out.println("\n=== 测试 fillData 逻辑 ===");
        testFillDataLogic(formJson, nameField, Map.of("age", "25"));

        // 测试getNextInstruction逻辑
        System.out.println("\n=== 测试 getNextInstruction 逻辑 ===");
        testGetNextInstructionLogic(false);
        testGetNextInstructionLogic(true);

        System.out.println("\n=== 简化版测试完成 ===");
        System.out.println("✅ 只需要两个方法就能完成所有表单处理逻辑！");
        System.out.println("✅ 不再需要记录总数和步骤，大大简化了代码！");
    }

    /**
     * 测试简化后的表单处理逻辑
     */
    private static void testSimplifiedLogic(String scenario, String formJson, Map<String, Object> filledFields) {
        System.out.println("场景：" + scenario);
        System.out.println("已填写字段：" + filledFields);

        // 🎯 核心方法1：获取需要填写的表单块
        String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);
        
        // 🎯 核心方法2：检查表单是否完成
        boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, filledFields);
        
        if (nextFormBlock.isEmpty()) {
            System.out.println("✅ 表单所有字段已填写完成");
            System.out.println("返回表单：{}");
        } else {
            System.out.println("📝 需要填写的表单块：有内容");
        }
        
        System.out.println("formCompleted: " + isCompleted);
        System.out.println("---");
    }

    /**
     * 测试简化后的 fillData 逻辑
     */
    private static void testFillDataLogic(String formJson, Map<String, Object> existingFields, Map<String, Object> submitData) {
        System.out.println("原有字段：" + existingFields);
        System.out.println("提交数据：" + submitData);

        // 合并已填写的数据
        Map<String, Object> allFilledFields = new HashMap<>(existingFields);
        allFilledFields.putAll(submitData);
        
        System.out.println("合并后字段：" + allFilledFields);

        // 🎯 只需要一个方法检查表单完成状态
        boolean isFormCompleted = FormStepProcessor.isFormCompleted(formJson, allFilledFields);
        
        System.out.println("formCompleted: " + isFormCompleted);
        
        if (isFormCompleted) {
            System.out.println("🎉 表单填写完成！");
        } else {
            System.out.println("📝 表单继续填写");
        }
    }

    /**
     * 测试简化后的 getNextInstruction 逻辑
     */
    private static void testGetNextInstructionLogic(boolean formCompleted) {
        System.out.println("formCompleted: " + formCompleted);

        if (formCompleted) {
            System.out.println("表单已完成，继续执行下一个指令 -> NEXT_INSTRUCTION");
        } else {
            System.out.println("表单还有字段需要填写，保持当前组件 -> KEEP_AT_PRESENT");
        }
    }

    /**
     * 演示简化前后的对比
     */
    public static void showComparison() {
        System.out.println("\n=== 简化前后对比 ===");
        
        System.out.println("【简化前】需要维护的状态：");
        System.out.println("- formStepCount (表单分组总数)");
        System.out.println("- formStepIndex (表单分组当前数)");
        System.out.println("- formCompleted (表单是否完成)");
        System.out.println("- 复杂的步骤计算逻辑");
        System.out.println("- 手动管理步骤跳转");
        
        System.out.println("\n【简化后】只需要：");
        System.out.println("- formCompleted (表单是否完成)");
        System.out.println("- getNextFormBlockToFill() 方法");
        System.out.println("- isFormCompleted() 方法");
        
        System.out.println("\n【优势】：");
        System.out.println("✅ 代码更简洁");
        System.out.println("✅ 逻辑更清晰");
        System.out.println("✅ 维护成本更低");
        System.out.println("✅ 不容易出错");
        System.out.println("✅ 支持跨步骤填写");
    }

    /**
     * 核心使用示例
     */
    public static void showCoreUsage() {
        System.out.println("\n=== 核心使用示例 ===");
        
        System.out.println("// doExecute() 方法");
        System.out.println("String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);");
        System.out.println("boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, filledFields);");
        System.out.println("chatProcessDTO.setFormCompleted(isCompleted);");
        
        System.out.println("\n// fillData() 方法");
        System.out.println("boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, allFilledFields);");
        System.out.println("chatProcessDTO.setFormCompleted(isCompleted);");
        
        System.out.println("\n// getNextInstruction() 方法");
        System.out.println("if (chatProcessDTO.getFormCompleted()) {");
        System.out.println("    return super.getNextInstruction(componentDataContext);");
        System.out.println("} else {");
        System.out.println("    return InstructionConstant.KEEP_AT_PRESENT.getCode();");
        System.out.println("}");
    }
}
