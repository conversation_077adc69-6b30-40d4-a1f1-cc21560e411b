package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FieldShowComponentEngine extends AbstractComponentEngine {

    private final BizInstanceFieldsService bizInstanceFieldsService;
    private static final String CODE = "FieldExtraction";

    public FieldShowComponentEngine(BizInstanceFieldsService bizInstanceFieldsService) {
        this.bizInstanceFieldsService = bizInstanceFieldsService;
    }

    @Override
    protected ComponentRunVO doExecute() {
        log.info("FieldShowComponentEngine execute");
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());


        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO =
                bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);
        // 获取AI提取字段
        Map<String, Object> aiExtractFields = chatProcessDTO.getAiExtractFields();

        // 处理 formJson
        String formJson = bizInstanceFieldsVO.getFormJson();
        JSONObject resultJson = FormStepProcessor.processFormJson(formJson, aiExtractFields);

        // 输出结果
        bizInstanceFieldsVO.setFormJson(JSONObject.toJSONString(resultJson));
        bizInstanceFieldsVO.setFormObj(JSON.toJSONString(aiExtractFields));

        // 组装结果
        ComponentRunVO.RenderData fieldFormRenderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(bizInstanceFieldsVO)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(fieldFormRenderData);
        vo.setRenderData(renderDataList);
        vo.setTips("表单提取展示组件");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        Object submitData = componentDataContext.getSubmitData();
        log.info("submitData:{}", JSON.toJSONString(submitData));
        @SuppressWarnings("unchecked")
        Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
        if (submitDataMap != null) {
            BizInstanceFieldsVO bizInstanceFieldsVO =
                    bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId(), true);
            String formObj = bizInstanceFieldsVO.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(submitDataMap);
                bizInstanceFieldsVO.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFieldsVO.setFormObj(JSON.toJSONString(submitDataMap));
            }
            BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            bizInstanceFields.setFormObj(bizInstanceFieldsVO.getFormObj());
            bizInstanceFieldsService.update(bizInstanceFields);
        }
    }
}
