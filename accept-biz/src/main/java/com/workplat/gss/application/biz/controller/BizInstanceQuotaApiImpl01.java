//package com.workplat.gss.application.biz.controller;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.collection.CollectionUtil;
//import com.alibaba.fastjson2.JSON;
//import com.google.common.collect.ListMultimap;
//import com.google.common.collect.Multimaps;
//import com.workplat.gss.application.api.api.BizInstanceSituationApi;
//import com.workplat.gss.application.api.dto.BizInstanceQuotaDto;
//import com.workplat.gss.application.biz.service.BizInstanceNetWorkWindowService;
//import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
//import com.workplat.gss.application.dubbo.entity.BizInstanceOption;
//import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
//import com.workplat.gss.application.dubbo.service.*;
//import com.workplat.gss.application.dubbo.vo.*;
//import com.workplat.gss.common.core.annotation.Idempotent;
//import com.workplat.gss.common.core.autowired.GssAutowired;
//import com.workplat.gss.common.core.exception.BusinessException;
//import com.workplat.gss.common.core.response.ResponseData;
//import com.workplat.gss.common.script.model.instance.QuotaBindInput;
//import com.workplat.gss.common.script.model.instance.QuotaBindOutput;
//import com.workplat.gss.common.script.model.instance.QuotaVerifyInput;
//import com.workplat.gss.common.script.model.instance.QuotaVerifyOutput;
//import com.workplat.gss.common.script.model.instance.ext.MatterAcceptQuota;
//import com.workplat.gss.script.dubbo.constants.ScriptConstants;
//import com.workplat.gss.script.dubbo.loader.ScriptRunnerService;
//import com.workplat.gss.script.dubbo.service.ConfScriptManageBindService;
//import com.workplat.gss.service.item.dubbo.matter.constant.BindingStatusEnum;
//import com.workplat.gss.service.item.dubbo.matter.constant.BindingTypeEnum;
//import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterPublic;
//import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.stream.Collectors;
//
//
//@Slf4j
//@Controller
//public class BizInstanceQuotaApiImpl01 implements BizInstanceSituationApi {
//
//    @Autowired
//    private BizInstanceInfoService bizInstanceInfoService;
//    @GssAutowired
//    private ConfMatterPublicService confMatterPublicService;
//    @Autowired
//    private BizInstanceQuotaService bizInstanceQuotaService;
//    @Autowired
//    private BizInstanceOptionService bizInstanceOptionService;
//    @Autowired
//    private BizInstanceMaterialService bizInstanceMaterialService;
//    @Autowired
//    private BizInstanceDocumentService bizInstanceDocumentService;
//    @Autowired
//    private BizInstanceResultService bizInstanceResultService;
//    @Autowired
//    private BizInstanceNetWorkWindowService bizInstanceNetWorkWindowService;
//    @Autowired
//    private BizInstanceFieldsService bizInstanceFieldsService;
//    @Autowired
//    private ScriptRunnerService scriptRunnerService;
//    @Autowired
//    private ConfScriptManageBindService confScriptManageBindService;
//
//    // 创建线程池
//    static ExecutorService executor = new ThreadPoolExecutor(10, 50,
//            60L, TimeUnit.SECONDS,
//            new LinkedBlockingQueue<Runnable>());
//
//    @Transactional
//    @Idempotent(expireTime = 5)
//    @Override
//    public ResponseData<List<BizInstanceQuotaVO>> save(List<BizInstanceQuotaDto> dto) {
//        String instanceId = dto.get(0).getInstanceId();
//        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
//        String matterPublicId = bizInstanceInfo.getMatterPublicId();
//        ConfMatterPublic confMatterPublic = confMatterPublicService.queryById(matterPublicId);
//        List<ConfMatterAcceptQuotaVO> voList = JSON.parseArray(confMatterPublic.getSituationJson(), ConfMatterAcceptQuotaVO.class);
//
//        //<editor-fold desc="脚本-指标校验">
//        try {
//            // 拿到脚本指标绑定脚本id
//            ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = voList.get(0);
//            if (confMatterAcceptQuotaVO.getIsBindAfterScript() != null && confMatterAcceptQuotaVO.getIsBindAfterScript()) {
//                String quotaVerifyScriptId = confMatterAcceptQuotaVO.getAfterScripts() == null ? "" : confMatterAcceptQuotaVO.getAfterScripts();
//                String[] quotaVerifyScriptIds = quotaVerifyScriptId.split(",");
//                for (String scriptId : quotaVerifyScriptIds) {
//                    // todo 输入参数应该是指标列表,如何校验暂时不知道,等做的时候再问
//                    QuotaVerifyInput quotaVerifyInput = new QuotaVerifyInput();
//                    // todo 输出参数是校验结果,也不知道,等做的时候再问
//                    QuotaVerifyOutput quotaVerifyOutput = scriptRunnerService.run(scriptId
//                            , ScriptConstants.ScriptType.QUOTA_VERIFY.getMethod()
//                            , QuotaVerifyInput.class
//                            , quotaVerifyInput);
//                    if (quotaVerifyOutput == null) {
//                        continue;
//                    }
//                }
//
//            }
//
//        } catch (Exception e) {
//        }
//        //</editor-fold>
//
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("=(instanceId)", instanceId);
//        bizInstanceQuotaService.deleteByParams(hashMap);
//        List<BizInstanceQuota> confMatterAcceptQuota = BeanUtil.copyToList(dto, BizInstanceQuota.class);
//        recursiveSave(confMatterAcceptQuota, null, instanceId);
//        List<BizInstanceQuota> newEntity = bizInstanceQuotaService.queryForList(hashMap);
//        List<BizInstanceOption> opt = new ArrayList<>();
//        recursiveList(newEntity, opt);
//
//        List<ConfMatterAcceptQuotaVO> list = new ArrayList<>();
//        recursive(voList, list);
//
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> materialMap = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> fieldMap = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> documentMap = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> matterMap = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> netWorkWindowMap = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        for (ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO : list) {
//            List<ConfMatterAcceptOptionVO> options = confMatterAcceptQuotaVO.getOptions();
//            if (CollectionUtil.isNotEmpty(options)) {
//                for (ConfMatterAcceptOptionVO option : options) {
//                    if (CollectionUtil.isNotEmpty(option.getBinding())) {
//                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : option.getBinding()) {
//                            if (BindingTypeEnum.MATERIAL.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                materialMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
//                            }
//                            if (BindingTypeEnum.FIELD.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                fieldMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
//                            }
//                            if (BindingTypeEnum.DOCUMENT.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                documentMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
//                            }
//                            if (BindingTypeEnum.MATTER.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                matterMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
//                            }
//                            if (BindingTypeEnum.NetWorkWindow.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                netWorkWindowMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        ListMultimap<String, String> material = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, String> field = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, String> document = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, String> matter = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        ListMultimap<String, String> netWorkWindow = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        for (BizInstanceOption bizInstanceOption : opt) {
//            List<ConfMatterAcceptOptionBindingVO> materialList = materialMap.get(bizInstanceOption.getOptId());
//            for (ConfMatterAcceptOptionBindingVO bindingVO : materialList) {
//                material.put(bindingVO.getContentId(), bindingVO.getBindingType());
//            }
//            List<ConfMatterAcceptOptionBindingVO> fieldList = fieldMap.get(bizInstanceOption.getOptId());
//            for (ConfMatterAcceptOptionBindingVO bindingVO : fieldList) {
//                field.put(bindingVO.getContentId(), bindingVO.getBindingType());
//            }
//            List<ConfMatterAcceptOptionBindingVO> documentList = documentMap.get(bizInstanceOption.getOptId());
//            for (ConfMatterAcceptOptionBindingVO bindingVO : documentList) {
//                document.put(bindingVO.getContentId(), bindingVO.getBindingType());
//            }
//            List<ConfMatterAcceptOptionBindingVO> matterList = matterMap.get(bizInstanceOption.getOptId());
//            for (ConfMatterAcceptOptionBindingVO bindingVO : matterList) {
//                matter.put(bindingVO.getContentId(), bindingVO.getBindingType());
//            }
//            List<ConfMatterAcceptOptionBindingVO> netWorkWindowList = netWorkWindowMap.get(bizInstanceOption.getOptId());
//            for (ConfMatterAcceptOptionBindingVO bindingVO : netWorkWindowList) {
//                netWorkWindow.put(bindingVO.getContentId(), bindingVO.getBindingType());
//            }
//        }
//        List<String> documentId = new ArrayList<>();
//        List<String> documentIds = new ArrayList<>();
//        document.asMap().forEach((key, value) -> {
//            List<String> type = new ArrayList<>(value);
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
//                documentIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEALL.getName()))) {
//                documentIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
//                documentId.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDALL.getName()))) {
//                documentId.add(key);
//            }
//        });
//        List<String> matterId = new ArrayList<>();
//        List<String> matterIds = new ArrayList<>();
//        matter.asMap().forEach((key, value) -> {
//            List<String> type = new ArrayList<>(value);
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
//                matterIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEALL.getName()))) {
//                matterIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
//                matterId.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDALL.getName()))) {
//                matterId.add(key);
//            }
//        });
//        List<String> materialId = new ArrayList<>();
//        List<String> materialIds = new ArrayList<>();
//        material.asMap().forEach((key, value) -> {
//            List<String> type = new ArrayList<>(value);
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
//                materialIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEALL.getName()))) {
//                materialIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
//                materialId.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDALL.getName()))) {
//                materialId.add(key);
//            }
//        });
//        List<String> fieldId = new ArrayList<>();
//        List<String> fieldIds = new ArrayList<>();
//        field.asMap().forEach((key, value) -> {
//            List<String> type = new ArrayList<>(value);
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
//                fieldIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEALL.getName()))) {
//                fieldIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
//                fieldId.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDALL.getName()))) {
//                fieldId.add(key);
//            }
//        });
//        List<String> netWorkWindowId = new ArrayList<>();
//        List<String> netWorkWindowIds = new ArrayList<>();
//        netWorkWindow.asMap().forEach((key, value) -> {
//            List<String> type = new ArrayList<>(value);
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
//                netWorkWindowIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.REMOVEALL.getName()))) {
//                netWorkWindowIds.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
//                netWorkWindowId.add(key);
//            }
//            if (type.stream().allMatch(element -> element.equals(BindingStatusEnum.ADDALL.getName()))) {
//                netWorkWindowId.add(key);
//            }
//        });
//        /**
//         * 阻塞多个线程，释放条件是有5个线程完成了任务
//         * 用countDownLatch.await();阻塞某些线程，直到有五个线程调用countDownLatch.countDown();
//         */
//        CountDownLatch countDownLatch = new CountDownLatch(5);
//        // 初始化instance对象
//        long startTime = System.nanoTime();
//        initBizInstanceObject(countDownLatch, instanceId, fieldId, fieldIds
//                , materialId, materialIds, documentId, documentIds
//                , matterId, matterIds, netWorkWindowId, netWorkWindowIds);
//        // 阻塞
//        try {
//            countDownLatch.await();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        long endTime = System.nanoTime();
//        double durationMs = (endTime - startTime) / 1_000_000.0;
//        log.info("%s 执行耗时: %.2f ms%n", "情形选择初始化instance对象", durationMs);
//        return ResponseData.success("保存成功", BeanUtil.copyToList(newEntity, BizInstanceQuotaVO.class));
//    }
//
//    /**
//     * 初始化instance对象
//     *
//     * @param instanceId
//     * @param fieldId
//     * @param fieldIds
//     * @param countDownLatch
//     * @param materialId
//     * @param materialIds
//     * @param documentId
//     * @param documentIds
//     * @param matterId
//     * @param matterIds
//     * @param netWorkWindowId
//     * @param netWorkWindowIds
//     */
//    private void initBizInstanceObject(CountDownLatch countDownLatch, String instanceId
//            , List<String> fieldId, List<String> fieldIds
//            , List<String> materialId, List<String> materialIds
//            , List<String> documentId, List<String> documentIds
//            , List<String> matterId, List<String> matterIds
//            , List<String> netWorkWindowId, List<String> netWorkWindowIds) {
//        executor.execute(() -> {
//            try {
//                bizInstanceFieldsService.initBizInstanceFields(instanceId, String.join(",", fieldId), String.join(",", fieldIds));
//            } catch (Exception e) {
//                log.error("初始化instance对象失败", e);
//                throw new BusinessException(e);
//            } finally {
//                countDownLatch.countDown();
//            }
//        });
//        executor.execute(() -> {
//            try {
//                bizInstanceMaterialService.initInstanceMaterial(instanceId, String.join(",", materialId), String.join(",", materialIds));
//            } catch (Exception e) {
//                log.error("初始化instance对象失败", e);
//                throw new BusinessException(e);
//            } finally {
//                countDownLatch.countDown();
//            }
//        });
//        executor.execute(() -> {
//            try {
//                bizInstanceDocumentService.initInstanceDocument(instanceId, String.join(",", documentId), String.join(",", documentIds));
//            } catch (Exception e) {
//                log.error("初始化instance对象失败", e);
//                throw new BusinessException(e);
//            } finally {
//                countDownLatch.countDown();
//            }
//        });
//        executor.execute(() -> {
//            try {
//                bizInstanceResultService.initInstanceResult(instanceId, String.join(",", matterId), String.join(",", matterIds));
//            } catch (Exception e) {
//                log.error("初始化instance对象失败", e);
//                throw new BusinessException(e);
//            } finally {
//                countDownLatch.countDown();
//            }
//        });
//        executor.execute(() -> {
//            try {
//                bizInstanceNetWorkWindowService.initInstanceNetWorkWindow(instanceId, String.join(",", netWorkWindowId), String.join(",", netWorkWindowIds));
//            } catch (Exception e) {
//                log.error("初始化instance对象失败", e);
//                throw new BusinessException(e);
//            } finally {
//                countDownLatch.countDown();
//            }
//        });
//    }
//
//    @Override
//    public ResponseData<BizInstanceSituationVO> query(String instanceId) {
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("=(instanceId)", instanceId);
//        List<BizInstanceQuota> newEntity = bizInstanceQuotaService.queryForList(hashMap);
//        List<BizInstanceQuotaVO> bizInstanceQuotaVOS = BeanUtil.copyToList(newEntity, BizInstanceQuotaVO.class);
//        BizInstanceSituationVO bizInstanceSituationVO = new BizInstanceSituationVO();
//        Collections.reverse(bizInstanceQuotaVOS);
//        bizInstanceSituationVO.setQuotaVOList(bizInstanceQuotaVOS);
//        List<BizInstanceResultVO> instanceResult = bizInstanceResultService.getInstanceResult(instanceId);
//        String matterName = instanceResult.get(0).getMatterName();
//        HashMap<String, List<BizInstanceResultVO>> map = new HashMap<>();
//        map.put(matterName, instanceResult);
//        bizInstanceSituationVO.setResult(map);
//        List<BizInstanceMaterialVO> instanceMaterial = bizInstanceMaterialService.getInstanceMaterial(instanceId);
//        bizInstanceSituationVO.setMaterialVOList(instanceMaterial);
//        return ResponseData.success("保存成功", bizInstanceSituationVO);
//    }
//
//    /**
//     * 递归保存多层树结构。
//     *
//     * @param confMatterAcceptQuotas 需要保存的多层树结构
//     */
//    @Transactional()
//    public void recursiveSave(List<BizInstanceQuota> confMatterAcceptQuotas, String id, String instance) {
//
//        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
//            for (BizInstanceQuota quota : confMatterAcceptQuotas) {
//                // 保存当前节点
//                BizInstanceQuota confMatterAcceptQuota = bizInstanceQuotaService.create(quota);
//                confMatterAcceptQuota.setOptionId(id);
//                confMatterAcceptQuota.setInstanceId(instance);
//                List<BizInstanceOption> options = confMatterAcceptQuota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    for (BizInstanceOption option : options) {
//                        option.setQuotaId(confMatterAcceptQuota.getId());
//                        BizInstanceOption confMatterAcceptOption = bizInstanceOptionService.create(option);
//                        // 递归保存子节点
//                        recursiveSave(confMatterAcceptOption.getQuotas(), confMatterAcceptOption.getId(), null);
//                    }
//                }
//
//            }
//        }
//    }
//
//    @Override
//    public ResponseData<List<ConfMatterAcceptQuotaVO>> get(String instanceId) {
//        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
//        String matterPublicId = bizInstanceInfo.getMatterPublicId();
//        ConfMatterPublic confMatterPublic = confMatterPublicService.queryById(matterPublicId);
//        List<ConfMatterAcceptQuotaVO> voList = JSON.parseArray(confMatterPublic.getSituationJson(), ConfMatterAcceptQuotaVO.class);
//        List<ConfMatterAcceptQuotaVO> list = new ArrayList<>();
//        recursive(voList, list);
//        Map<String, ConfMatterAcceptQuotaVO> map = list.stream()
//                .collect(Collectors.toMap(
//                        ConfMatterAcceptQuotaVO::getCode, // key
//                        vo -> vo, // value, 如果希望值保持原样，则可以简化为第二个参数vo -> vo
//                        (existing, replacement) -> existing)); // 合并函数，当有重复key时决定保留哪个值，这里设置为保留第一次出现的值
//
//
//        //绑定指标map
//        ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash = Multimaps.newListMultimap(
//                new HashMap<>(), // 使用 HashMap 作为底层存储
//                ArrayList::new // 使用 ArrayList 作为值的容器
//        );
//        recursiveQuery(list, hash);
//        recur(voList, hash, map);
//        sort(voList);
//
//        if (CollUtil.isEmpty(voList)) {
//            return ResponseData.success("保存成功", voList);
//        }
//
//        //<editor-fold desc="脚本-指标绑定">
//        try {
//            // 拿到脚本指标绑定脚本id
//            ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = voList.get(0);
//            if (confMatterAcceptQuotaVO.getIsBindScript() == null || !confMatterAcceptQuotaVO.getIsBindScript()) {
//                return ResponseData.success("保存成功", voList);
//            }
//            String quotaBindScriptId = confMatterAcceptQuotaVO.getScripts() == null ? "" : confMatterAcceptQuotaVO.getScripts();
//            String[] quotaBindScriptIds = quotaBindScriptId.split(",");
//            // 执行脚本
//            QuotaBindInput quotaBindInput = new QuotaBindInput();
//            quotaBindInput.setQuotaList(BeanUtil.copyToList(voList, MatterAcceptQuota.class));
//            QuotaBindOutput quotaBindOutput = null;
//            for (String scriptId : quotaBindScriptIds) {
//                quotaBindOutput = scriptRunnerService.run(scriptId
//                        , ScriptConstants.ScriptType.QUOTA_BIND.getMethod()
//                        , QuotaBindInput.class
//                        , quotaBindInput);
//                if (quotaBindOutput == null) {
//                    continue;
//                }
//                quotaBindInput.setQuotaList(quotaBindOutput.getQuotaList());
//            }
//            // 处理默认勾选部分情形
//            if (quotaBindOutput != null && CollUtil.isNotEmpty(quotaBindOutput.getQuotaList())) {
//                return ResponseData.success(BeanUtil.copyToList(quotaBindOutput.getQuotaList(), ConfMatterAcceptQuotaVO.class));
//            }
//        } catch (Exception e) {
//        }
//        //</editor-fold>
//
//        return ResponseData.success(BeanUtil.copyToList(voList, ConfMatterAcceptQuotaVO.class));
//    }
//
//    public void sort(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas) {
//
//        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
//            List<ConfMatterAcceptQuotaVO> quotaVOS = confMatterAcceptQuotas.stream().sorted(Comparator.comparing(ConfMatterAcceptQuotaVO::getSort))
//                    .toList();
//            for (ConfMatterAcceptQuotaVO quota : quotaVOS) {
//                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    List<ConfMatterAcceptOptionVO> collect = options.stream().sorted(Comparator.comparing(ConfMatterAcceptOptionVO::getSort))
//                            .toList();
//                    for (ConfMatterAcceptOptionVO option : collect) {
//                        // 递归
//                        sort(option.getQuotas());
//                    }
//                }
//
//            }
//        }
//    }
//
//    public void recur(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash, Map<String, ConfMatterAcceptQuotaVO> map) {
//
//        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
//            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
//                String code = quota.getCode();
//                ListMultimap<String, String> multimap = Multimaps.newListMultimap(
//                        new HashMap<>(), // 使用 HashMap 作为底层存储
//                        ArrayList::new // 使用 ArrayList 作为值的容器
//                );
//                List<ConfMatterAcceptOptionBindingVO> confMatterAcceptOptionBindingVOS = hash.get(code);
//                if (CollectionUtil.isNotEmpty(confMatterAcceptOptionBindingVOS)) {
//                    for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : confMatterAcceptOptionBindingVOS) {
//                        multimap.put(confMatterAcceptOptionBindingVO.getBindingType(), confMatterAcceptOptionBindingVO.getOptionId());
//                    }
//                }
//                List<String> addOnly = multimap.get(BindingStatusEnum.ADDONLY.getName());
//                List<String> addAll = multimap.get(BindingStatusEnum.ADDALL.getName());
//                List<String> removeOnly = multimap.get(BindingStatusEnum.REMOVEONLY.getName());
//                List<String> removeAll = multimap.get(BindingStatusEnum.REMOVEALL.getName());
//
//                quota.setBindOptionId(addOnly);
//                quota.setBindOptionIds(addAll);
//                quota.setBindExcludeOptionId(removeOnly);
//                quota.setBindExcludeOptionIds(removeAll);
//                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    for (ConfMatterAcceptOptionVO option : options) {
//                        // 递归
//                        List<ConfMatterAcceptQuotaVO> quotas = option.getQuotas();
//                        List<ConfMatterAcceptQuotaVO> quo = new ArrayList<>();
//                        recur(quotas, hash, map);
//                        List<ConfMatterAcceptOptionBindingVO> binding = option.getBinding();
//                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : binding) {
//                            if ("指标".equals(confMatterAcceptOptionBindingVO.getType())) {
//                                ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = map.get(confMatterAcceptOptionBindingVO.getQuotaCode());
//                                if (CollectionUtil.isNotEmpty(quotas)) {
//                                    quotas.add(confMatterAcceptQuotaVO);
//                                    option.setQuotas(quotas);
//                                } else {
//                                    quo.add(confMatterAcceptQuotaVO);
//                                    option.setQuotas(quo);
//                                }
//                            }
//                        }
//
//                    }
//                }
//
//            }
//        }
//    }
//
//    public void recursiveQuery(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash) {
//
//        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
//            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
//                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    for (ConfMatterAcceptOptionVO option : options) {
//                        List<ConfMatterAcceptOptionBindingVO> binding = option.getBinding();
//                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : binding) {
//                            if (BindingTypeEnum.QUOTA.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
//                                hash.put(confMatterAcceptOptionBindingVO.getQuotaCode(), confMatterAcceptOptionBindingVO);
//                            }
//                        }
//
//                        // 递归
//                        recursiveQuery(option.getQuotas(), hash);
//                    }
//                }
//
//            }
//        }
//    }
//
//    public void recursive(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, List<ConfMatterAcceptQuotaVO> confMatterAcceptQuota) {
//
//        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
//            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
//                confMatterAcceptQuota.add(quota);
//                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    for (ConfMatterAcceptOptionVO option : options) {
//                        // 递归
//                        recursive(option.getQuotas(), confMatterAcceptQuota);
//                    }
//                }
//
//            }
//        }
//    }
//
//    public void recursiveList(List<BizInstanceQuota> bizInstanceQuotaList, List<BizInstanceOption> optionList) {
//        if (CollectionUtil.isNotEmpty(bizInstanceQuotaList)) {
//            for (BizInstanceQuota quota : bizInstanceQuotaList) {
//                List<BizInstanceOption> options = quota.getOptions();
//                if (CollectionUtil.isNotEmpty(options)) {
//                    optionList.addAll(options);
//                    for (BizInstanceOption option : options) {
//                        // 递归
//                        recursiveList(option.getQuotas(), optionList);
//                    }
//                }
//
//            }
//        }
//    }
//}
