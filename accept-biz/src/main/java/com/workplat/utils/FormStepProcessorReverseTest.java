package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * FormStepProcessor 反向方法测试类
 */
public class FormStepProcessorReverseTest {

    public static void main(String[] args) {
        // 创建一个简单的测试表单JSON
        String testFormJson = """
            {
                "tableName": "test_table",
                "formAttribute": {},
                "beforeFunction": "",
                "submitFunction": "",
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "基本信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "name",
                                    "label": "姓名",
                                    "modelValue": "张三"
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "age",
                                    "label": "年龄",
                                    "modelValue": "25"
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "email",
                                    "label": "邮箱",
                                    "modelValue": "<EMAIL>"
                                }
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "联系信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "phone",
                                    "label": "电话",
                                    "modelValue": "13800138000"
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "address",
                                    "label": "地址",
                                    "modelValue": "北京市"
                                }
                            }
                        ]
                    }
                ]
            }
            """;

        System.out.println("=== 原始表单JSON ===");
        System.out.println(JSONObject.parseObject(testFormJson).toJSONString());

        // 测试原方法：只保留name和phone字段
        System.out.println("\n=== 测试原方法：只保留name和phone字段 ===");
        Map<String, Object> includeFields = new HashMap<>();
        includeFields.put("name", "张三");
        includeFields.put("phone", "13800138000");
        
        JSONObject includeResult = FormStepProcessor.processFormJson(testFormJson, includeFields);
        System.out.println("保留结果：" + includeResult.toJSONString());

        // 测试新方法：移除name和phone字段
        System.out.println("\n=== 测试新方法：移除name和phone字段 ===");
        Map<String, Object> excludeFields = new HashMap<>();
        excludeFields.put("name", "");
        excludeFields.put("phone", "");
        
        JSONObject excludeResult = FormStepProcessor.removeFieldsFromFormJson(testFormJson, excludeFields);
        System.out.println("移除结果：" + excludeResult.toJSONString());

        // 测试边界情况：移除所有字段
        System.out.println("\n=== 测试边界情况：移除所有字段 ===");
        Map<String, Object> excludeAllFields = new HashMap<>();
        excludeAllFields.put("name", "");
        excludeAllFields.put("age", "");
        excludeAllFields.put("email", "");
        excludeAllFields.put("phone", "");
        excludeAllFields.put("address", "");
        
        JSONObject excludeAllResult = FormStepProcessor.removeFieldsFromFormJson(testFormJson, excludeAllFields);
        System.out.println("移除所有字段结果：" + excludeAllResult.toJSONString());

        // 测试边界情况：移除不存在的字段
        System.out.println("\n=== 测试边界情况：移除不存在的字段 ===");
        Map<String, Object> excludeNonExistentFields = new HashMap<>();
        excludeNonExistentFields.put("nonexistent", "");
        
        JSONObject excludeNonExistentResult = FormStepProcessor.removeFieldsFromFormJson(testFormJson, excludeNonExistentFields);
        System.out.println("移除不存在字段结果：" + excludeNonExistentResult.toJSONString());

        System.out.println("\n=== 测试完成 ===");
        System.out.println("新方法 removeFieldsFromFormJson 工作正常！");
    }
}
