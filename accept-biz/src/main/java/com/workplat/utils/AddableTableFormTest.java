package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.*;

/**
 * 测试可新增表单的特殊处理逻辑
 * 针对可新增表单不做字段移除，直接返回完整结构
 */
public class AddableTableFormTest {

    public static void main(String[] args) {
        System.out.println("=== 可新增表单特殊处理测试 ===");

        // 模拟包含可新增表格的表单JSON
        String formJson = """
            {
                "tableName": "addable_table_form",
                "formAttribute": {},
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "基本信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "name", "label": "姓名", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetCanAddTable",
                                "props": {"field": "BMMzc1H_l", "label": "可新增表格"},
                                "child": [
                                    {
                                        "componentName": "ANetInput",
                                        "props": {"field": "cqsyfs", "label": "出让使用方式", "modelValue": ""}
                                    },
                                    {
                                        "componentName": "ANetInput",
                                        "props": {"field": "cqsyqfsbzsm", "label": "备注说明", "modelValue": ""}
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "其他信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "phone", "label": "电话", "modelValue": ""}
                            }
                        ]
                    }
                ]
            }
            """;

        // 测试场景1：没有填写任何字段
        System.out.println("\n--- 场景1：没有填写任何字段 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        testAddableTableProcessing("没有填写任何字段", formJson, emptyFields);

        // 测试场景2：只填写了普通字段
        System.out.println("\n--- 场景2：只填写了普通字段 ---");
        Map<String, Object> normalFields = new HashMap<>();
        normalFields.put("name", "张三");
        testAddableTableProcessing("只填写了普通字段", formJson, normalFields);

        // 测试场景3：填写了可新增表格的部分数据
        System.out.println("\n--- 场景3：填写了可新增表格的部分数据 ---");
        Map<String, Object> partialTableFields = new HashMap<>();
        partialTableFields.put("name", "李四");
        
        // 可新增表格数据：{"BMMzc1H_l": [{"cqsyfs": "1"}]}
        List<Map<String, Object>> partialTableData = new ArrayList<>();
        Map<String, Object> partialRow = new HashMap<>();
        partialRow.put("cqsyfs", "1");
        partialTableData.add(partialRow);
        partialTableFields.put("BMMzc1H_l", partialTableData);
        
        testAddableTableProcessing("填写了可新增表格的部分数据", formJson, partialTableFields);

        // 测试场景4：填写了可新增表格的完整数据
        System.out.println("\n--- 场景4：填写了可新增表格的完整数据 ---");
        Map<String, Object> completeTableFields = new HashMap<>();
        completeTableFields.put("name", "王五");
        
        // 可新增表格完整数据：{"BMMzc1H_l": [{"cqsyfs": "1", "cqsyqfsbzsm": "2121213"}]}
        List<Map<String, Object>> completeTableData = new ArrayList<>();
        Map<String, Object> completeRow = new HashMap<>();
        completeRow.put("cqsyfs", "1");
        completeRow.put("cqsyqfsbzsm", "2121213");
        completeTableData.add(completeRow);
        completeTableFields.put("BMMzc1H_l", completeTableData);
        
        testAddableTableProcessing("填写了可新增表格的完整数据", formJson, completeTableFields);

        // 测试场景5：填写了所有字段
        System.out.println("\n--- 场景5：填写了所有字段 ---");
        Map<String, Object> allFields = new HashMap<>();
        allFields.put("name", "赵六");
        allFields.put("phone", "13800138000");
        allFields.put("BMMzc1H_l", completeTableData);
        
        testAddableTableProcessing("填写了所有字段", formJson, allFields);

        // 测试processFormJson方法（保留字段）
        System.out.println("\n=== 测试 processFormJson 方法（保留字段） ===");
        testProcessFormJsonWithAddableTable(formJson, partialTableFields);

        // 测试removeFieldsFromFormJson方法（移除字段）
        System.out.println("\n=== 测试 removeFieldsFromFormJson 方法（移除字段） ===");
        testRemoveFieldsFromFormJsonWithAddableTable(formJson, partialTableFields);

        System.out.println("\n=== 测试完成 ===");
        System.out.println("✅ 可新增表单组件已正确处理：不做字段移除，直接返回完整结构");
    }

    /**
     * 测试可新增表单处理逻辑
     */
    private static void testAddableTableProcessing(String scenario, String formJson, Map<String, Object> filledFields) {
        System.out.println("场景：" + scenario);
        System.out.println("已填写字段：" + filledFields);

        // 🎯 使用智能表单处理器
        String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);
        boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, filledFields);
        
        if (nextFormBlock.isEmpty()) {
            System.out.println("✅ 表单所有字段已填写完成");
        } else {
            System.out.println("📝 需要填写的表单块：有内容");
            
            // 检查是否包含可新增表格
            if (nextFormBlock.contains("ANetCanAddTable")) {
                System.out.println("🎯 包含可新增表格组件，已保留完整结构");
            }
            
            // 简化显示
            if (nextFormBlock.length() > 300) {
                System.out.println("表单块预览：" + nextFormBlock.substring(0, 300) + "...");
            } else {
                System.out.println("表单块内容：" + nextFormBlock);
            }
        }
        
        System.out.println("formCompleted: " + isCompleted);
        System.out.println("---");
    }

    /**
     * 测试 processFormJson 方法对可新增表格的处理
     */
    private static void testProcessFormJsonWithAddableTable(String formJson, Map<String, Object> filledFields) {
        System.out.println("测试 processFormJson 方法（保留指定字段）");
        System.out.println("已填写字段：" + filledFields);

        JSONObject result = FormStepProcessor.processFormJson(formJson, filledFields);
        String resultStr = result.toJSONString();
        
        System.out.println("处理结果：");
        if (resultStr.contains("ANetCanAddTable")) {
            System.out.println("✅ 可新增表格组件已保留完整结构");
        } else {
            System.out.println("❌ 可新增表格组件未正确处理");
        }
        
        if (resultStr.length() > 500) {
            System.out.println("结果预览：" + resultStr.substring(0, 500) + "...");
        } else {
            System.out.println("完整结果：" + resultStr);
        }
    }

    /**
     * 测试 removeFieldsFromFormJson 方法对可新增表格的处理
     */
    private static void testRemoveFieldsFromFormJsonWithAddableTable(String formJson, Map<String, Object> excludeFields) {
        System.out.println("测试 removeFieldsFromFormJson 方法（移除指定字段）");
        System.out.println("要移除的字段：" + excludeFields);

        JSONObject result = FormStepProcessor.removeFieldsFromFormJson(formJson, excludeFields);
        String resultStr = result.toJSONString();
        
        System.out.println("处理结果：");
        if (resultStr.contains("ANetCanAddTable")) {
            System.out.println("✅ 可新增表格组件已保留（不被移除）");
        } else {
            System.out.println("❌ 可新增表格组件被错误移除");
        }
        
        if (resultStr.length() > 500) {
            System.out.println("结果预览：" + resultStr.substring(0, 500) + "...");
        } else {
            System.out.println("完整结果：" + resultStr);
        }
    }

    /**
     * 创建您的可新增表格数据格式示例
     */
    public static Map<String, Object> createAddableTableData() {
        Map<String, Object> filledFieldMap = new HashMap<>();
        
        // 普通字段
        filledFieldMap.put("name", "测试用户");
        
        // 可新增表格数据：{"BMMzc1H_l": [{"cqsyfs": "1", "cqsyqfsbzsm": "2121213"}]}
        List<Map<String, Object>> tableData = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("cqsyfs", "1");
        row1.put("cqsyqfsbzsm", "2121213");
        tableData.add(row1);
        
        // 可以添加多行数据
        Map<String, Object> row2 = new HashMap<>();
        row2.put("cqsyfs", "2");
        row2.put("cqsyqfsbzsm", "第二行数据");
        tableData.add(row2);
        
        filledFieldMap.put("BMMzc1H_l", tableData);
        
        return filledFieldMap;
    }

    /**
     * 验证可新增表格组件识别逻辑
     */
    public static void testComponentNameRecognition() {
        System.out.println("\n=== 测试组件名称识别 ===");
        
        String[] testComponentNames = {
            "ANetCanAddTable",
            "ANetAddableTable", 
            "CanAddTable",
            "SomeCanAddComponent",
            "AddableFormTable",
            "ANetInput",
            "ANetSelect",
            null
        };
        
        for (String componentName : testComponentNames) {
            // 这里我们无法直接调用私有方法，但可以通过行为来验证
            System.out.println("组件名称: " + componentName + " -> " + 
                (componentName != null && (componentName.contains("CanAdd") || componentName.contains("Addable")) ? "可新增组件" : "普通组件"));
        }
    }
}
