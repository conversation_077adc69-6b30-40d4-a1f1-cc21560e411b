package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 表单步骤完成度检测测试类
 * 演示如何根据已填写字段判断步骤完成状态并自动跳转到下一步
 */
public class FormStepCompletionTest {

    public static void main(String[] args) {
        // 创建一个多步骤表单JSON示例
        String multiStepFormJson = """
            {
                "tableName": "multi_step_form",
                "formAttribute": {},
                "beforeFunction": "",
                "submitFunction": "",
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第一步：基本信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "name",
                                    "label": "姓名",
                                    "modelValue": ""
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "age",
                                    "label": "年龄",
                                    "modelValue": ""
                                }
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第二步：联系信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "phone",
                                    "label": "电话",
                                    "modelValue": ""
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "email",
                                    "label": "邮箱",
                                    "modelValue": ""
                                }
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第三步：地址信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "address",
                                    "label": "地址",
                                    "modelValue": ""
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "zipcode",
                                    "label": "邮编",
                                    "modelValue": ""
                                }
                            }
                        ]
                    }
                ]
            }
            """;

        System.out.println("=== 表单步骤完成度检测演示 ===");
        
        // 场景1：没有填写任何字段
        System.out.println("\n--- 场景1：没有填写任何字段 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        demonstrateStepCompletion(multiStepFormJson, emptyFields);

        // 场景2：只填写了第一步的部分字段
        System.out.println("\n--- 场景2：只填写了第一步的部分字段 ---");
        Map<String, Object> partialStep1Fields = new HashMap<>();
        partialStep1Fields.put("name", "张三");
        demonstrateStepCompletion(multiStepFormJson, partialStep1Fields);

        // 场景3：完成了第一步的所有字段
        System.out.println("\n--- 场景3：完成了第一步的所有字段 ---");
        Map<String, Object> completeStep1Fields = new HashMap<>();
        completeStep1Fields.put("name", "张三");
        completeStep1Fields.put("age", "25");
        demonstrateStepCompletion(multiStepFormJson, completeStep1Fields);

        // 场景4：完成了前两步
        System.out.println("\n--- 场景4：完成了前两步 ---");
        Map<String, Object> completeTwoStepsFields = new HashMap<>();
        completeTwoStepsFields.put("name", "张三");
        completeTwoStepsFields.put("age", "25");
        completeTwoStepsFields.put("phone", "13800138000");
        completeTwoStepsFields.put("email", "<EMAIL>");
        demonstrateStepCompletion(multiStepFormJson, completeTwoStepsFields);

        // 场景5：完成了所有步骤
        System.out.println("\n--- 场景5：完成了所有步骤 ---");
        Map<String, Object> allCompleteFields = new HashMap<>();
        allCompleteFields.put("name", "张三");
        allCompleteFields.put("age", "25");
        allCompleteFields.put("phone", "13800138000");
        allCompleteFields.put("email", "<EMAIL>");
        allCompleteFields.put("address", "北京市朝阳区");
        allCompleteFields.put("zipcode", "100000");
        demonstrateStepCompletion(multiStepFormJson, allCompleteFields);

        // 演示您的具体需求场景
        System.out.println("\n=== 您的具体需求场景演示 ===");
        demonstrateYourUseCase(multiStepFormJson, completeStep1Fields);
    }

    /**
     * 演示步骤完成度检测
     */
    private static void demonstrateStepCompletion(String formJson, Map<String, Object> filledFields) {
        System.out.println("已填写字段：" + filledFields);
        
        int totalSteps = FormStepProcessor.getFormStepCount(formJson);
        System.out.println("总步骤数：" + totalSteps);
        
        // 检查每个步骤的完成状态
        for (int step = 1; step <= totalSteps; step++) {
            boolean isCompleted = FormStepProcessor.isStepCompleted(formJson, step, filledFields);
            System.out.println("步骤" + step + "是否完成：" + isCompleted);
        }
        
        // 获取下一个需要填写的步骤
        int nextStep = FormStepProcessor.getNextIncompleteStep(formJson, filledFields);
        if (nextStep == -1) {
            System.out.println("✅ 所有步骤已完成！");
        } else {
            System.out.println("➡️ 下一个需要填写的步骤：" + nextStep);
        }
    }

    /**
     * 演示您的具体需求场景
     */
    private static void demonstrateYourUseCase(String formJson, Map<String, Object> filledFields) {
        System.out.println("模拟您的需求场景：");
        System.out.println("已填写字段：" + filledFields);
        
        // 获取步骤1的数据
        JSONObject stepData = FormStepProcessor.getStepData(formJson, 1);
        System.out.println("步骤1数据：" + stepData.toJSONString());
        
        // 移除已填写的字段
        JSONObject remainingFields = FormStepProcessor.removeFieldsFromFormJson(stepData.toJSONString(), filledFields);
        System.out.println("移除已填写字段后：" + remainingFields.toJSONString());
        
        // 判断是否还有字段可以填写
        boolean isStep1Completed = FormStepProcessor.isStepCompleted(formJson, 1, filledFields);
        System.out.println("步骤1是否完成：" + isStep1Completed);
        
        if (isStep1Completed) {
            System.out.println("✅ 步骤1已完成，获取下一个步骤");
            int nextStep = FormStepProcessor.getNextIncompleteStep(formJson, filledFields);
            if (nextStep != -1) {
                JSONObject nextStepData = FormStepProcessor.getStepData(formJson, nextStep);
                System.out.println("下一步骤" + nextStep + "数据：" + nextStepData.toJSONString());
            }
        } else {
            System.out.println("❌ 步骤1还有字段需要填写");
            JSONObject incompleteFields = FormStepProcessor.getIncompleteFieldsInStep(formJson, 1, filledFields);
            System.out.println("未完成的字段：" + incompleteFields.toJSONString());
        }
    }
}
