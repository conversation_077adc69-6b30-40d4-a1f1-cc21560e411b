{"tableName": "", "renderList": [{"icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "platform": "all", "type": "formArea", "needSpecial": false, "componentName": "ANetFormArea", "props": {"bgColor": "#3A81FF", "title": "身份证信息", "titleSize": 22, "titleColor": "#0E0D0D", "barColor": "", "isHidden": false, "functions": [], "isShowButton": true, "arrowColor": "#000000", "show": true, "field": "f2BgEPZ1Q"}, "events": {}, "child": [{"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "申请人姓名", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sqrxm", "key": "sqrxm", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputsqrxm"}, {"name": "foucs事件", "value": "", "key": "foucssqrxm"}, {"name": "change事件", "value": "", "key": "changesqrxm"}, {"name": "click事件", "value": "", "key": "clicksqrxm"}], "id": "cb547071b02547bb851b5178364ed5c1"}, "events": {}, "child": [], "rules": [{"required": true, "message": "申请人姓名不能为空", "trigger": "blur"}], "id": "23i6E6Qfx"}, {"icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "platform": "all", "componentName": "a-net-radio", "needSpecial": false, "key": "", "needRules": true, "props": {"label": "性别", "labelDescribe": "", "tableWidth": "", "field": "<PERSON><PERSON><PERSON>", "modelValue": "1", "disabled": false, "labelWidth": "", "labelHeight": "", "placeholder": "请选择", "optionKey": "", "functions": [{"name": "change事件", "value": "", "key": "change<PERSON>bie"}], "options": [{"value": "男", "label": "男", "disabled": false}, {"value": "女", "label": "女", "disabled": false}], "key": "<PERSON><PERSON><PERSON>", "id": "b07478ece5614490b7f05bab3d9fed30"}, "events": {}, "child": [], "rules": [{"required": true, "message": "性别不能为空", "trigger": "blur"}], "id": "lQ68tqOJC"}, {"icon": "icon-timeSelector", "name": "日期选择器", "needRules": true, "platform": "all", "componentName": "a-net-date-picker", "needSpecial": true, "specialName": "DataPickerAtribute", "props": {"field": "csrq", "label": "出生日期", "labelDescribe": "", "tableWidth": "", "labelWidth": "", "labelHeight": "", "isTimeLimit": true, "readonly": false, "clearable": false, "disabled": false, "key": "csrq", "modelValue": "2025-06-18", "type": "date", "rangeSeparator": "-", "controlsPosition": "", "placeholder": "请选择时间", "startPlaceholder": "请选择开始时间", "endPlaceholder": "请选择结束时间", "format": "YYYY-MM-DD", "valueFormat": "YYYY-MM-DD", "functions": [{"name": "change事件", "value": "", "key": "changecsrq"}], "id": "7dc15f858391417486c522975fdfed1b"}, "events": {}, "child": [], "rules": [{"required": true, "message": "出生日期不能为空", "trigger": "blur"}], "id": "PM4Kz29Ir"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "身份证号码", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sfzhm", "key": "sfzhm", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputsfzhm"}, {"name": "foucs事件", "value": "", "key": "foucssfzhm"}, {"name": "change事件", "value": "", "key": "changesfzhm"}, {"name": "click事件", "value": "", "key": "clicksfzhm"}], "id": "9ed74ca6b359402d8fc56b7040847014"}, "events": {}, "child": [], "rules": [{"required": true, "message": "身份证号码不能为空", "trigger": "blur"}], "id": "gjW_UnUV1"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "住址", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sqrzz", "key": "sqrzz", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "input0eNpvct2A"}, {"name": "foucs事件", "value": "", "key": "foucs0eNpvct2A"}, {"name": "change事件", "value": "", "key": "change0eNpvct2A"}, {"name": "click事件", "value": "", "key": "click0eNpvct2A"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "输入框不能为空", "trigger": "blur"}], "id": "vTWejDSQGw"}, {"icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "platform": "all", "type": "addTable", "needSpecial": false, "componentName": "ANetCanAddTable", "props": {"functions": [{"name": "delete事件", "value": "", "key": "delete3hai9FuKS"}, {"name": "add事件", "value": "", "key": "add3hai9FuKS"}], "title": "售卖人", "deleteText": "删除", "addText": "新增", "defaultLine": 0, "isShowAsTable": false, "isShowIndex": true, "isSelection": false, "isAddByDialog": false, "isShowOutBorder": true, "isShowInnerBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "innerWidth": 1, "outerWidth": 1, "key": "", "isNeedMax": false, "isNeedMin": false, "showColumns": "", "min": 1, "max": 1, "field": "3hai9FuKS"}, "events": {}, "child": [{"child": [{"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "售卖人姓名", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "td2-bbc1e", "key": "", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputtd2-bbc1e"}, {"name": "foucs事件", "value": "", "key": "foucstd2-bbc1e"}, {"name": "change事件", "value": "", "key": "changetd2-bbc1e"}, {"name": "click事件", "value": "", "key": "clicktd2-bbc1e"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "输入框不能为空", "trigger": "blur"}], "id": "Zs7_hMBvTo"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "售卖人职位", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "fxS8zJi54", "key": "", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputfxS8zJi54"}, {"name": "foucs事件", "value": "", "key": "foucsfxS8zJi54"}, {"name": "change事件", "value": "", "key": "changefxS8zJi54"}, {"name": "click事件", "value": "", "key": "clickfxS8zJi54"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "输入框不能为空", "trigger": "blur"}], "id": "oYvZbOixSA"}]}], "rules": [], "id": "WOljkUeqzt"}], "rules": [], "id": "KfLjT25pj1"}, {"icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "platform": "all", "type": "formArea", "needSpecial": false, "componentName": "ANetFormArea", "props": {"bgColor": "#3A81FF", "title": "申领信息", "titleSize": 22, "titleColor": "#0E0D0D", "barColor": "", "isHidden": false, "functions": [], "isShowButton": true, "arrowColor": "#000000", "show": false, "field": "eiZooxlHx"}, "events": {}, "child": [{"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "联系电话", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sqrlxdh", "key": "sqrlxdh", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputsqrlxdh"}, {"name": "foucs事件", "value": "", "key": "foucssqrlxdh"}, {"name": "change事件", "value": "", "key": "changesqrlxdh"}, {"name": "click事件", "value": "", "key": "clicksqrlxdh"}], "id": "da798c9d7b4b4c6d9d0b40fd23b0c5f3"}, "events": {}, "child": [], "rules": [{"required": true, "message": "联系电话不能为空", "trigger": "blur"}], "id": "jDqMj2HX0"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "事由", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "blsy", "key": "", "modelValue": "旅游", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": true, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputTNm42YY-t"}, {"name": "foucs事件", "value": "", "key": "foucsTNm42YY-t"}, {"name": "change事件", "value": "", "key": "changeTNm42YY-t"}, {"name": "click事件", "value": "", "key": "clickTNm42YY-t"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "输入框不能为空", "trigger": "blur"}], "id": "mlMBALhTWy"}, {"icon": "icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "级联选择器", "platform": "all", "componentName": "a-net-cascader", "needSpecial": false, "needRules": true, "props": {"label": "前往地点", "labelDescribe": "", "labelWidth": "", "tableWidth": "", "labelHeight": "", "field": "qwdd", "key": "", "modelValue": "", "cascaderOption": "", "placeholder": "请选择", "disabled": false, "clearable": false, "filterable": false, "optionKey": "", "functions": [{"name": "change事件", "value": "", "key": "changeBA2fjINrZ"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "前往地点不能为空", "trigger": "blur"}], "id": "DeN3tmwh9y"}, {"icon": "icon-timeSelector", "name": "日期选择器", "needRules": true, "platform": "all", "componentName": "a-net-date-picker", "needSpecial": true, "specialName": "DataPickerAtribute", "props": {"field": "qwrq", "label": "前往日期", "labelDescribe": "", "tableWidth": "", "labelWidth": "", "labelHeight": "", "isTimeLimit": true, "readonly": false, "clearable": false, "disabled": false, "key": "qwrq", "modelValue": "2025-06-18", "type": "date", "rangeSeparator": "-", "controlsPosition": "", "placeholder": "请选择时间", "startPlaceholder": "请选择开始时间", "endPlaceholder": "请选择结束时间", "format": "YYYY-MM-DD", "valueFormat": "YYYY-MM-DD", "functions": [{"name": "change事件", "value": "", "key": "changeqwrq"}], "id": "c0fadc4973fa49ebacb4efe888e3c9ad"}, "events": {}, "child": [], "rules": [{"required": true, "message": "前往日期不能为空", "trigger": "blur"}], "id": "C3J3W-duh"}, {"icon": "icon-timeSelector", "name": "日期选择器", "needRules": true, "platform": "all", "componentName": "a-net-date-picker", "needSpecial": true, "specialName": "DataPickerAtribute", "props": {"field": "fhrq", "label": "返回日期", "labelDescribe": "", "tableWidth": "", "labelWidth": "", "labelHeight": "", "isTimeLimit": true, "readonly": false, "clearable": false, "disabled": false, "key": "fhrq", "modelValue": "2025-06-18", "type": "date", "rangeSeparator": "-", "controlsPosition": "", "placeholder": "请选择时间", "startPlaceholder": "请选择开始时间", "endPlaceholder": "请选择结束时间", "format": "YYYY-MM-DD", "valueFormat": "YYYY-MM-DD", "functions": [{"name": "change事件", "value": "", "key": "changefhrq"}], "id": "f926996e48314a96b7786ae8dec79bd1"}, "events": {}, "child": [], "rules": [{"required": true, "message": "返回日期不能为空", "trigger": "blur"}], "id": "zjBWcelWo"}, {"icon": "icon-kaiguan3", "name": "邮寄地址", "platform": "all", "type": "addressArea", "componentName": "ANetMailingAddress", "needSpecial": false, "props": {"isHidden": false, "functions": [], "show": true, "listInterface": "", "saveInterface": "", "field": "3-bVk6NXm"}, "events": {}, "child": [{"icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "platform": "all", "componentName": "a-net-radio", "needSpecial": false, "key": "", "needRules": true, "props": {"label": "请选择凭证领取方式", "labelDescribe": "", "tableWidth": "", "field": "lqpzfs", "isNeedHide": false, "showById": "", "showByValue": "", "modelValue": "邮寄", "disabled": false, "labelWidth": "", "labelHeight": "", "placeholder": "请选择", "optionKey": "", "functions": [{"name": "change事件", "value": "", "key": "changeU-HQO138H"}], "options": [{"value": "现场领取", "label": "现场领取", "disabled": false}, {"value": "邮寄", "label": "邮寄", "disabled": false}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "领取方式不能为空", "trigger": "blur"}], "id": "zkr99vPpng"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-button-title", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "收件人姓名", "labelWidth": "", "isNeedHide": true, "showById": "lqpzfs", "showByValue": "邮寄", "titleButtonText": "从地址簿导入", "listInterface": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sjrxm", "key": "", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "input3-bVk6NXmname"}, {"name": "foucs事件", "value": "", "key": "foucs3-bVk6NXmname"}, {"name": "change事件", "value": "", "key": "change3-bVk6NXmname"}, {"name": "click事件", "value": "", "key": "click3-bVk6NXmname"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "收件人姓名不能为空", "trigger": "blur"}], "id": "zJxpu8-V4D"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input-number", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "收件人手机号码", "labelWidth": "", "isNeedHide": true, "showById": "lqpzfs", "showByValue": "邮寄", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "sjrsjhm", "key": "", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "input3-bVk6NXmmobile"}, {"name": "foucs事件", "value": "", "key": "foucs3-bVk6NXmmobile"}, {"name": "change事件", "value": "", "key": "change3-bVk6NXmmobile"}, {"name": "click事件", "value": "", "key": "click3-bVk6NXmmobile"}]}, "events": {}, "child": [], "rules": [{"required": true, "message": "收件人手机号码不能为空", "trigger": "blur"}, {"pattern": "", "message": "", "trigger": "blur"}], "id": "TaeBEyG71u"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "邮寄地址", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "yjdz", "key": "lqpzfs", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputiYynB-LNt"}, {"name": "foucs事件", "value": "", "key": "foucsiYynB-LNt"}, {"name": "change事件", "value": "", "key": "changeiYynB-LNt"}, {"name": "click事件", "value": "", "key": "clickiYynB-LNt"}], "isNeedHide": true, "showById": "lqpzfs", "showByValue": "邮寄"}, "events": {}, "child": [], "rules": [{"required": true, "message": "邮寄地址不能为空", "trigger": "blur"}], "id": "NjJOXO7xLF"}, {"icon": "icon-input", "name": "输入框", "componentName": "a-net-input", "needSpecial": false, "platform": "all", "needRules": true, "props": {"label": "邮政编码", "labelWidth": "", "isDesensitization": "", "labelDescribe": "", "labelHeight": "", "field": "yzbm", "key": "yzbm", "modelValue": "", "placeholder": "请在此处输入内容", "tableWidth": "", "clearable": false, "disabled": false, "readonly": false, "maxlength": 50, "default": "", "isShowLimit": false, "showAppendBtn": false, "buttonText": "", "appendBtnColor": "#2c8ef1", "functions": [{"name": "input事件", "value": "console.log(form)", "key": "inputyzbm"}, {"name": "foucs事件", "value": "", "key": "foucsyzbm"}, {"name": "change事件", "value": "", "key": "changeyzbm"}, {"name": "click事件", "value": "", "key": "clickyzbm"}], "id": "9d1a3bbc7bae435ca20f8265b4fa30ca", "isNeedHide": true, "showById": "lqpzfs", "showByValue": "邮寄"}, "events": {}, "child": [], "rules": [{"required": true, "message": "邮政编码不能为空", "trigger": "blur"}], "id": "dyo6G6Ian"}], "rules": [], "id": "9sNqsksnN-"}], "rules": [], "id": "7L3w2dvUUE"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "demoFormName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "{}", "submitFunction": "{}"}