package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能表单处理器测试类
 * 演示如何根据表单数据和字段填写数据，直接定位到具体的表单块并只保留未填写的数据
 */
public class SmartFormProcessorTest {

    public static void main(String[] args) {
        // 创建一个复杂的多步骤表单
        String complexFormJson = """
            {
                "tableName": "complex_form",
                "formAttribute": {},
                "beforeFunction": "",
                "submitFunction": "",
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第一步：个人基本信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "name",
                                    "label": "姓名",
                                    "modelValue": "",
                                    "required": true
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "idCard",
                                    "label": "身份证号",
                                    "modelValue": "",
                                    "required": true
                                }
                            },
                            {
                                "componentName": "ANetSelect",
                                "props": {
                                    "field": "gender",
                                    "label": "性别",
                                    "modelValue": "",
                                    "required": false
                                }
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第二步：联系方式"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "phone",
                                    "label": "手机号码",
                                    "modelValue": "",
                                    "required": true
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "email",
                                    "label": "电子邮箱",
                                    "modelValue": "",
                                    "required": false
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "wechat",
                                    "label": "微信号",
                                    "modelValue": "",
                                    "required": false
                                }
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {
                            "title": "第三步：地址信息"
                        },
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "province",
                                    "label": "省份",
                                    "modelValue": "",
                                    "required": true
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "city",
                                    "label": "城市",
                                    "modelValue": "",
                                    "required": true
                                }
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {
                                    "field": "address",
                                    "label": "详细地址",
                                    "modelValue": "",
                                    "required": true
                                }
                            }
                        ]
                    }
                ]
            }
            """;

        System.out.println("=== 智能表单处理器演示 ===");

        // 场景1：用户刚开始填写表单
        System.out.println("\n--- 场景1：用户刚开始填写表单 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        demonstrateSmartFormProcessing("刚开始填写", complexFormJson, emptyFields);

        // 场景2：用户填写了部分第一步字段
        System.out.println("\n--- 场景2：用户填写了部分第一步字段 ---");
        Map<String, Object> partialStep1 = new HashMap<>();
        partialStep1.put("name", "张三");
        demonstrateSmartFormProcessing("填写了姓名", complexFormJson, partialStep1);

        // 场景3：用户完成了第一步，开始第二步
        System.out.println("\n--- 场景3：用户完成了第一步，开始第二步 ---");
        Map<String, Object> completeStep1 = new HashMap<>();
        completeStep1.put("name", "张三");
        completeStep1.put("idCard", "110101199001011234");
        completeStep1.put("gender", "男");
        demonstrateSmartFormProcessing("完成第一步", complexFormJson, completeStep1);

        // 场景4：用户填写了跨步骤的字段
        System.out.println("\n--- 场景4：用户填写了跨步骤的字段 ---");
        Map<String, Object> crossStepFields = new HashMap<>();
        crossStepFields.put("name", "李四");
        crossStepFields.put("phone", "13800138000");
        crossStepFields.put("province", "北京市");
        demonstrateSmartFormProcessing("跨步骤填写", complexFormJson, crossStepFields);

        // 场景5：用户几乎完成了所有字段
        System.out.println("\n--- 场景5：用户几乎完成了所有字段 ---");
        Map<String, Object> almostComplete = new HashMap<>();
        almostComplete.put("name", "王五");
        almostComplete.put("idCard", "110101199001011234");
        almostComplete.put("gender", "女");
        almostComplete.put("phone", "13800138000");
        almostComplete.put("email", "<EMAIL>");
        almostComplete.put("province", "上海市");
        almostComplete.put("city", "上海市");
        // 缺少 address 字段
        demonstrateSmartFormProcessing("几乎完成", complexFormJson, almostComplete);

        // 演示直接获取需要填写的表单块
        System.out.println("\n=== 直接获取需要填写的表单块 ===");
        JSONObject incompleteFormBlock = FormStepProcessor.getFormBlockWithIncompleteFields(complexFormJson, partialStep1);
        System.out.println("需要填写的表单块：" + incompleteFormBlock.toJSONString());
    }

    /**
     * 演示智能表单处理
     */
    private static void demonstrateSmartFormProcessing(String scenario, String formJson, Map<String, Object> filledFields) {
        System.out.println("场景：" + scenario);
        System.out.println("已填写字段：" + filledFields);

        // 使用智能表单处理器
        FormStepProcessor.FormProcessResult result = FormStepProcessor.processFormWithFilledFields(formJson, filledFields);
        
        System.out.println("处理结果：" + result);
        System.out.println("总步骤数：" + result.getTotalSteps());
        System.out.println("各步骤完成状态：" + result.getStepStatusMap());
        
        if (result.isAllCompleted()) {
            System.out.println("🎉 恭喜！所有步骤都已完成！");
        } else {
            System.out.println("📝 下一个需要填写的步骤：" + result.getNextIncompleteStep());
            System.out.println("该步骤需要填写的字段：");
            System.out.println(result.getNextStepFormData().toJSONString());
            
            // 获取具体步骤的详细信息
            FormStepProcessor.FormStepResult stepResult = FormStepProcessor.getFormStepWithIncompleteFields(
                formJson, result.getNextIncompleteStep(), filledFields);
            System.out.println("步骤详细信息：" + stepResult);
        }
        
        System.out.println("---");
    }

    /**
     * 演示您的具体使用场景
     */
    public static JSONObject getNextFormBlockToFill(String formJson, Map<String, Object> filledFields) {
        System.out.println("=== 您的具体使用场景 ===");
        System.out.println("输入 - 表单JSON：" + (formJson.length() > 100 ? formJson.substring(0, 100) + "..." : formJson));
        System.out.println("输入 - 已填写字段：" + filledFields);
        
        // 一键获取需要填写的表单块
        JSONObject result = FormStepProcessor.getFormBlockWithIncompleteFields(formJson, filledFields);
        
        System.out.println("输出 - 需要填写的表单块：" + result.toJSONString());
        
        if (result.isEmpty()) {
            System.out.println("✅ 所有字段都已填写完成！");
        } else {
            System.out.println("📝 还有字段需要填写");
        }
        
        return result;
    }
}
