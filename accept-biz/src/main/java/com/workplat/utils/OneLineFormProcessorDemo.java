package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 一行代码完成智能表单处理演示
 * 展示如何根据表单数据和字段填写数据，直接定位到具体的表单块并只保留未填写的数据
 */
public class OneLineFormProcessorDemo {

    public static void main(String[] args) {
        // 示例表单JSON
        String formJson = """
            {
                "tableName": "demo_form",
                "formAttribute": {},
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "基本信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "name", "label": "姓名", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "age", "label": "年龄", "modelValue": ""}
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "联系信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "phone", "label": "电话", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "email", "label": "邮箱", "modelValue": ""}
                            }
                        ]
                    }
                ]
            }
            """;

        System.out.println("=== 一行代码完成智能表单处理 ===");

        // 场景1：用户还没有填写任何字段
        System.out.println("\n--- 场景1：用户还没有填写任何字段 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        
        // 🎯 一行代码获取需要填写的表单块
        String nextFormBlock1 = FormStepProcessor.getNextFormBlockToFill(formJson, emptyFields);
        System.out.println("需要填写的表单块：" + nextFormBlock1);

        // 场景2：用户填写了姓名
        System.out.println("\n--- 场景2：用户填写了姓名 ---");
        Map<String, Object> nameField = new HashMap<>();
        nameField.put("name", "张三");
        
        // 🎯 一行代码获取需要填写的表单块
        String nextFormBlock2 = FormStepProcessor.getNextFormBlockToFill(formJson, nameField);
        System.out.println("需要填写的表单块：" + nextFormBlock2);

        // 场景3：用户完成了第一步
        System.out.println("\n--- 场景3：用户完成了第一步 ---");
        Map<String, Object> step1Complete = new HashMap<>();
        step1Complete.put("name", "张三");
        step1Complete.put("age", "25");
        
        // 🎯 一行代码获取需要填写的表单块
        String nextFormBlock3 = FormStepProcessor.getNextFormBlockToFill(formJson, step1Complete);
        System.out.println("需要填写的表单块：" + nextFormBlock3);

        // 场景4：用户完成了所有字段
        System.out.println("\n--- 场景4：用户完成了所有字段 ---");
        Map<String, Object> allComplete = new HashMap<>();
        allComplete.put("name", "张三");
        allComplete.put("age", "25");
        allComplete.put("phone", "13800138000");
        allComplete.put("email", "<EMAIL>");
        
        // 🎯 一行代码获取需要填写的表单块
        String nextFormBlock4 = FormStepProcessor.getNextFormBlockToFill(formJson, allComplete);
        System.out.println("需要填写的表单块：" + (nextFormBlock4.isEmpty() ? "所有字段已完成！" : nextFormBlock4));

        // 🎯 一行代码检查表单是否完成
        boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, allComplete);
        System.out.println("表单是否完成：" + isCompleted);

        System.out.println("\n=== 实际使用示例 ===");
        demonstrateRealWorldUsage(formJson);
    }

    /**
     * 演示实际使用场景
     */
    private static void demonstrateRealWorldUsage(String formJson) {
        System.out.println("模拟实际业务场景：");

        // 模拟用户逐步填写表单的过程
        Map<String, Object> userFields = new HashMap<>();

        // 步骤1：用户打开表单
        System.out.println("\n1. 用户打开表单");
        String formToShow = FormStepProcessor.getNextFormBlockToFill(formJson, userFields);
        System.out.println("   显示给用户的表单：" + formatFormForDisplay(formToShow));

        // 步骤2：用户填写了姓名
        System.out.println("\n2. 用户填写了姓名");
        userFields.put("name", "李四");
        formToShow = FormStepProcessor.getNextFormBlockToFill(formJson, userFields);
        System.out.println("   更新后的表单：" + formatFormForDisplay(formToShow));

        // 步骤3：用户填写了年龄
        System.out.println("\n3. 用户填写了年龄");
        userFields.put("age", "30");
        formToShow = FormStepProcessor.getNextFormBlockToFill(formJson, userFields);
        System.out.println("   更新后的表单：" + formatFormForDisplay(formToShow));

        // 步骤4：用户填写了电话
        System.out.println("\n4. 用户填写了电话");
        userFields.put("phone", "13900139000");
        formToShow = FormStepProcessor.getNextFormBlockToFill(formJson, userFields);
        System.out.println("   更新后的表单：" + formatFormForDisplay(formToShow));

        // 步骤5：用户填写了邮箱
        System.out.println("\n5. 用户填写了邮箱");
        userFields.put("email", "<EMAIL>");
        formToShow = FormStepProcessor.getNextFormBlockToFill(formJson, userFields);
        System.out.println("   更新后的表单：" + formatFormForDisplay(formToShow));

        // 检查是否完成
        if (FormStepProcessor.isFormCompleted(formJson, userFields)) {
            System.out.println("\n🎉 表单填写完成！可以提交了！");
        }
    }

    /**
     * 格式化表单显示（简化版）
     */
    private static String formatFormForDisplay(String formJson) {
        if (formJson.isEmpty()) {
            return "无需填写字段";
        }
        
        try {
            JSONObject form = JSONObject.parseObject(formJson);
            if (form.containsKey("renderList")) {
                return "包含 " + form.getJSONArray("renderList").size() + " 个表单区域";
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        
        return "有字段需要填写";
    }

    /**
     * 🎯 您的核心使用方法 - 复制这个方法到您的项目中
     */
    public static String getFormBlockForUser(String formJson, Map<String, Object> filledFields) {
        // 一行代码完成所有逻辑：
        // 1. 分析表单结构
        // 2. 检查已填写字段
        // 3. 定位到需要填写的步骤
        // 4. 移除已填写的字段
        // 5. 返回需要显示给用户的表单块
        return FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);
    }

    /**
     * 🎯 检查表单完成状态 - 复制这个方法到您的项目中
     */
    public static boolean checkFormCompletion(String formJson, Map<String, Object> filledFields) {
        return FormStepProcessor.isFormCompleted(formJson, filledFields);
    }
}
