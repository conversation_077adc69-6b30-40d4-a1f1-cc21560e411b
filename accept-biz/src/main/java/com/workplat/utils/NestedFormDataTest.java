package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;

import java.util.*;

/**
 * 测试可新增表格的嵌套数组数据格式处理
 */
public class NestedFormDataTest {

    public static void main(String[] args) {
        System.out.println("=== 可新增表格嵌套数组数据格式测试 ===");

        // 模拟包含可新增表格的表单JSON
        String formJson = """
            {
                "tableName": "nested_form",
                "formAttribute": {},
                "renderList": [
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "基本信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "name", "label": "姓名", "modelValue": ""}
                            },
                            {
                                "componentName": "ANetCanAddTable",
                                "props": {"field": "BMMzc1H_l", "label": "可新增表格"},
                                "child": [
                                    {
                                        "componentName": "ANetInput",
                                        "props": {"field": "cqsyfs", "label": "出让使用方式", "modelValue": ""}
                                    },
                                    {
                                        "componentName": "ANetInput",
                                        "props": {"field": "cqsyqfsbzsm", "label": "备注说明", "modelValue": ""}
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "componentName": "ANetFormArea",
                        "props": {"title": "其他信息"},
                        "child": [
                            {
                                "componentName": "ANetInput",
                                "props": {"field": "phone", "label": "电话", "modelValue": ""}
                            }
                        ]
                    }
                ]
            }
            """;

        // 测试场景1：空数据
        System.out.println("\n--- 场景1：空数据 ---");
        Map<String, Object> emptyFields = new HashMap<>();
        testNestedFormProcessing("空数据", formJson, emptyFields);

        // 测试场景2：只填写了简单字段
        System.out.println("\n--- 场景2：只填写了简单字段 ---");
        Map<String, Object> simpleFields = new HashMap<>();
        simpleFields.put("name", "张三");
        testNestedFormProcessing("只填写姓名", formJson, simpleFields);

        // 测试场景3：填写了可新增表格数据（您的数据格式）
        System.out.println("\n--- 场景3：填写了可新增表格数据 ---");
        Map<String, Object> nestedFields = new HashMap<>();
        nestedFields.put("name", "张三");
        
        // 可新增表格的数据格式：{"BMMzc1H_l": [{"cqsyfs": "1", "cqsyqfsbzsm": "2121213"}]}
        List<Map<String, Object>> tableData = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("cqsyfs", "1");
        row1.put("cqsyqfsbzsm", "2121213");
        tableData.add(row1);
        nestedFields.put("BMMzc1H_l", tableData);
        
        testNestedFormProcessing("填写了可新增表格", formJson, nestedFields);

        // 测试场景4：填写了部分可新增表格字段
        System.out.println("\n--- 场景4：填写了部分可新增表格字段 ---");
        Map<String, Object> partialNestedFields = new HashMap<>();
        partialNestedFields.put("name", "李四");
        
        List<Map<String, Object>> partialTableData = new ArrayList<>();
        Map<String, Object> partialRow = new HashMap<>();
        partialRow.put("cqsyfs", "2");  // 只填写了一个字段
        partialTableData.add(partialRow);
        partialNestedFields.put("BMMzc1H_l", partialTableData);
        
        testNestedFormProcessing("部分填写可新增表格", formJson, partialNestedFields);

        // 测试场景5：填写了所有字段
        System.out.println("\n--- 场景5：填写了所有字段 ---");
        Map<String, Object> allFields = new HashMap<>();
        allFields.put("name", "王五");
        allFields.put("phone", "13800138000");
        
        List<Map<String, Object>> completeTableData = new ArrayList<>();
        Map<String, Object> completeRow = new HashMap<>();
        completeRow.put("cqsyfs", "3");
        completeRow.put("cqsyqfsbzsm", "完整备注");
        completeTableData.add(completeRow);
        allFields.put("BMMzc1H_l", completeTableData);
        
        testNestedFormProcessing("填写了所有字段", formJson, allFields);

        // 测试辅助方法
        System.out.println("\n=== 测试辅助方法 ===");
        testHelperMethods();

        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 测试嵌套表单处理逻辑
     */
    private static void testNestedFormProcessing(String scenario, String formJson, Map<String, Object> filledFields) {
        System.out.println("场景：" + scenario);
        System.out.println("已填写字段：" + filledFields);

        // 🎯 使用智能表单处理器
        String nextFormBlock = FormStepProcessor.getNextFormBlockToFill(formJson, filledFields);
        boolean isCompleted = FormStepProcessor.isFormCompleted(formJson, filledFields);
        
        if (nextFormBlock.isEmpty()) {
            System.out.println("✅ 表单所有字段已填写完成");
        } else {
            System.out.println("📝 需要填写的表单块：有内容");
            // 可以打印部分内容来验证
            if (nextFormBlock.length() > 200) {
                System.out.println("表单块预览：" + nextFormBlock.substring(0, 200) + "...");
            } else {
                System.out.println("表单块内容：" + nextFormBlock);
            }
        }
        
        System.out.println("formCompleted: " + isCompleted);
        System.out.println("---");
    }

    /**
     * 测试辅助方法的功能
     */
    private static void testHelperMethods() {
        // 创建测试数据
        Map<String, Object> testFieldMap = new HashMap<>();
        testFieldMap.put("name", "张三");
        
        List<Map<String, Object>> tableData = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("cqsyfs", "1");
        row.put("cqsyqfsbzsm", "测试备注");
        tableData.add(row);
        testFieldMap.put("BMMzc1H_l", tableData);

        System.out.println("测试数据：" + testFieldMap);

        // 测试字段检查
        System.out.println("检查 'name' 字段：" + checkFieldExists("name", testFieldMap));
        System.out.println("检查 'cqsyfs' 字段：" + checkFieldExists("cqsyfs", testFieldMap));
        System.out.println("检查 'cqsyqfsbzsm' 字段：" + checkFieldExists("cqsyqfsbzsm", testFieldMap));
        System.out.println("检查 'nonexistent' 字段：" + checkFieldExists("nonexistent", testFieldMap));

        // 测试字段值获取
        System.out.println("获取 'name' 值：" + getFieldValueFromMap("name", testFieldMap));
        System.out.println("获取 'cqsyfs' 值：" + getFieldValueFromMap("cqsyfs", testFieldMap));
        System.out.println("获取 'cqsyqfsbzsm' 值：" + getFieldValueFromMap("cqsyqfsbzsm", testFieldMap));
    }

    /**
     * 模拟 isFieldFilled 方法的逻辑
     */
    private static boolean checkFieldExists(String field, Map<String, Object> fieldMap) {
        // 直接检查字段是否存在
        if (fieldMap.containsKey(field)) {
            return true;
        }
        
        // 检查嵌套数组结构中是否包含该字段
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            Object value = entry.getValue();
            
            // 如果值是数组，检查数组中的每个对象
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) value;
                for (Object item : list) {
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        if (itemMap.containsKey(field)) {
                            return true;
                        }
                    }
                }
            }
        }
        
        return false;
    }

    /**
     * 模拟 getFieldValue 方法的逻辑
     */
    private static Object getFieldValueFromMap(String field, Map<String, Object> fieldMap) {
        // 直接获取字段值
        if (fieldMap.containsKey(field)) {
            return fieldMap.get(field);
        }
        
        // 从嵌套数组结构中获取字段值
        for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
            Object value = entry.getValue();
            
            // 如果值是数组，检查数组中的每个对象
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> list = (List<Object>) value;
                for (Object item : list) {
                    if (item instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        if (itemMap.containsKey(field)) {
                            return itemMap.get(field);
                        }
                    }
                }
            }
        }
        
        return null;
    }

    /**
     * 创建您的数据格式示例
     */
    public static Map<String, Object> createYourDataFormat() {
        Map<String, Object> filledFieldMap = new HashMap<>();
        
        // 可新增表格的数据格式：{"BMMzc1H_l": [{"cqsyfs": "1", "cqsyqfsbzsm": "2121213"}]}
        List<Map<String, Object>> tableData = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("cqsyfs", "1");
        row.put("cqsyqfsbzsm", "2121213");
        tableData.add(row);
        
        filledFieldMap.put("BMMzc1H_l", tableData);
        
        return filledFieldMap;
    }
}
