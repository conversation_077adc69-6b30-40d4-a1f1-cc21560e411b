<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.workplat</groupId>
    <artifactId>ai-central-platform</artifactId>
    <name>ai-central-platform</name>
    <version>1.0.0-SNAPSHOT</version>
    <description>AI政务智能中枢平台</description>
    <packaging>pom</packaging>

    <modules>
        <module>accept-api</module>
        <module>accept-biz</module>
        <module>accept-dubbo</module>
    </modules>

    <!--版本管理-->
    <properties>
        <!--本项目版本-->
        <accept.cloud.version>1.0.0-SNAPSHOT</accept.cloud.version>
        <!--支撑平台基础包版本-->
        <gss.cloud.app.version>1.0.40-JOINT-UAT</gss.cloud.app.version>

        <spring.boot-version>3.2.4</spring.boot-version>
        <spring.cloud-version>2023.0.1</spring.cloud-version>
        <spring.cloud.alibaba-version>2023.0.1.0</spring.cloud.alibaba-version>
        <mysql.version>9.0.0</mysql.version>
        <spring.boot.admin-version>3.3.2</spring.boot.admin-version>
        <!-- maven相关 -->
        <java.version>21</java.version>
        <maven.compiler.plugin.version>3.13.0</maven.compiler.plugin.version>
        <maven.source.plugin.version>3.3.1</maven.source.plugin.version>
        <maven.jar.plugin.version>3.4.2</maven.jar.plugin.version>
        <maven.dependency.plugin.version>3.7.1</maven.dependency.plugin.version>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skipTests>true</skipTests>

        <deepoove.version>1.12.2</deepoove.version>

    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SpringBoot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringCloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringCloud Alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- MySQL -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring.boot.admin-version}</version>
            </dependency>
            <!-- spring-boot-starter-actuator -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.boot-version}</version>
            </dependency>
            <!-- gss-all -->
            <dependency>
                <groupId>com.workplat</groupId>
                <artifactId>gss-all</artifactId>
                <version>${gss.cloud.app.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml-schemas</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--word导出 -start-->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${deepoove.version}</version>
            </dependency>
            <!--word导出 -end-->

            <!-- 网关 -->
            <dependency>
                <groupId>com.workplat</groupId>
                <artifactId>workplat-gateway-sdk</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot-version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>workplat-release</id>
            <name>workplat Repository</name>
            <url>http://180.76.57.232:8081/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>workplat-snapshot</id>
            <name>workplat Repository</name>
            <url>http://180.76.57.232:8081/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>aliyun-public</id>
            <name>aliyun public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun-central</id>
            <name>aliyun central Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <!--maven私服-->
    <distributionManagement>
        <repository>
            <id>workplat-releases</id>
            <name>workplat release repository</name>
            <url>http://180.76.57.232:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>workplat-snapshots</id>
            <name>workplat snapshot repository</name>
            <url>http://180.76.57.232:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
